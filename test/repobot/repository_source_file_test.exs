defmodule Repobot.RepositorySourceFileTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.{RepositorySourceFile, Repo}

  import Repobot.Test.Fixtures

  describe "changeset/2" do
    test "validates required fields" do
      changeset = RepositorySourceFile.changeset(%RepositorySourceFile{}, %{})

      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).repository_id
      assert "can't be blank" in errors_on(changeset).source_file_id
    end

    test "validates unique constraint on repository_id and source_file_id" do
      user = create_user()
      repository = create_repository(%{user_id: user.id, name: "test-repo"})

      # Create source file with unique target path
      source_file =
        create_source_file(%{
          name: "test.ex",
          target_path: "lib/unique_path.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create first association
      {:ok, _} =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository.id,
          source_file_id: source_file.id
        })
        |> Repo.insert()

      # Try to create duplicate association with same repository and source file
      # Our validation will catch this before the database constraint
      changeset =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository.id,
          source_file_id: source_file.id
        })

      refute changeset.valid?

      assert "A source file with target path \"lib/unique_path.ex\" already exists in this repository" in errors_on(
               changeset
             ).source_file_id
    end

    test "validates unique target path per repository" do
      user = create_user()
      repository = create_repository(%{user_id: user.id, name: "test-repo"})

      # Create two source files with the same target path
      source_file1 =
        create_source_file(%{
          name: "readme1.md",
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "readme2.md",
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # First association should succeed
      {:ok, _} =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository.id,
          source_file_id: source_file1.id
        })
        |> Repo.insert()

      # Second association should fail due to duplicate target path
      changeset =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository.id,
          source_file_id: source_file2.id
        })

      refute changeset.valid?

      assert "A source file with target path \"README.md\" already exists in this repository" in errors_on(
               changeset
             ).source_file_id
    end

    test "allows same target path in different repositories" do
      user = create_user()
      repository1 = create_repository(%{user_id: user.id, name: "test-repo-1"})
      repository2 = create_repository(%{user_id: user.id, name: "test-repo-2"})

      # Create two source files with the same target path
      source_file1 =
        create_source_file(%{
          name: "readme1.md",
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "readme2.md",
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Both associations should succeed since they're in different repositories
      {:ok, _} =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository1.id,
          source_file_id: source_file1.id
        })
        |> Repo.insert()

      {:ok, _} =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository2.id,
          source_file_id: source_file2.id
        })
        |> Repo.insert()
    end

    test "allows same source file to be associated with multiple repositories" do
      user = create_user()
      repository1 = create_repository(%{user_id: user.id, name: "test-repo-1"})
      repository2 = create_repository(%{user_id: user.id, name: "test-repo-2"})

      source_file =
        create_source_file(%{
          name: "shared.md",
          target_path: "docs/shared.md",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Same source file should be associable with multiple repositories
      {:ok, _} =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository1.id,
          source_file_id: source_file.id
        })
        |> Repo.insert()

      {:ok, _} =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository2.id,
          source_file_id: source_file.id
        })
        |> Repo.insert()
    end

    test "validates against non-existent source file" do
      user = create_user()
      repository = create_repository(%{user_id: user.id, name: "test-repo"})
      non_existent_id = Ecto.UUID.generate()

      changeset =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository.id,
          source_file_id: non_existent_id
        })

      refute changeset.valid?
      assert "Source file not found" in errors_on(changeset).source_file_id
    end

    test "allows updating existing association without validation errors" do
      user = create_user()
      repository = create_repository(%{user_id: user.id, name: "test-repo"})
      source_file = create_source_file(%{name: "test.ex", user_id: user.id})

      # Create association
      {:ok, association} =
        %RepositorySourceFile{}
        |> RepositorySourceFile.changeset(%{
          repository_id: repository.id,
          source_file_id: source_file.id
        })
        |> Repo.insert()

      # Update the association (should not trigger duplicate validation)
      changeset =
        RepositorySourceFile.changeset(association, %{
          repository_id: repository.id,
          source_file_id: source_file.id
        })

      assert changeset.valid?
    end
  end
end
