defmodule Repobot.FilesUnifiedInterfaceTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.{Files, Repo}

  describe "repository files with content" do
    test "repository_files_with_content_query/0 returns query with content projection" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create repository file with content
      repo_file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.ex",
          content: "test content"
        })

      # Use the query to fetch files
      files = Files.repository_files_with_content_query() |> Repo.all()

      assert length(files) == 1
      file = hd(files)
      assert file.id == repo_file.id
      assert file.content == "test content"
    end

    test "load_repository_with_files/1 loads repository with files and content" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create repository files with content
      _file1 =
        create_repository_file(%{
          repository_id: repository.id,
          path: "file1.ex",
          content: "content 1"
        })

      _file2 =
        create_repository_file(%{
          repository_id: repository.id,
          path: "file2.ex",
          content: "content 2"
        })

      # Load repository with files
      loaded_repo = Files.load_repository_with_files(repository)

      assert length(loaded_repo.files) == 2

      file1 = Enum.find(loaded_repo.files, &(&1.path == "file1.ex"))
      file2 = Enum.find(loaded_repo.files, &(&1.path == "file2.ex"))

      assert file1.content == "content 1"
      assert file2.content == "content 2"
    end

    test "load_repository_with_files/1 with repository ID" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.ex",
          content: "test content"
        })

      # Load by ID
      loaded_repo = Files.load_repository_with_files(repository.id)

      assert loaded_repo.id == repository.id
      assert length(loaded_repo.files) == 1
      assert hd(loaded_repo.files).content == "test content"
    end

    test "load_repositories_with_files/1 loads multiple repositories" do
      user = create_user()
      repo1 = create_repository(%{user_id: user.id, name: "repo1"})
      repo2 = create_repository(%{user_id: user.id, name: "repo2"})

      _file1 =
        create_repository_file(%{
          repository_id: repo1.id,
          path: "test1.ex",
          content: "content 1"
        })

      _file2 =
        create_repository_file(%{
          repository_id: repo2.id,
          path: "test2.ex",
          content: "content 2"
        })

      # Load multiple repositories
      loaded_repos = Files.load_repositories_with_files([repo1, repo2])

      assert length(loaded_repos) == 2

      loaded_repo1 = Enum.find(loaded_repos, &(&1.id == repo1.id))
      loaded_repo2 = Enum.find(loaded_repos, &(&1.id == repo2.id))

      assert length(loaded_repo1.files) == 1
      assert length(loaded_repo2.files) == 1
      assert hd(loaded_repo1.files).content == "content 1"
      assert hd(loaded_repo2.files).content == "content 2"
    end

    test "preload_files_with_content/1 works with both single repo and list" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.ex",
          content: "test content"
        })

      # Test with single repository
      loaded_repo = Files.preload_files_with_content(repository)
      assert length(loaded_repo.files) == 1
      assert hd(loaded_repo.files).content == "test content"

      # Test with list of repositories
      loaded_repos = Files.preload_files_with_content([repository])
      assert length(loaded_repos) == 1
      assert length(hd(loaded_repos).files) == 1
    end
  end

  describe "source files with content" do
    test "source_files_with_content_query/0 returns query with content projection" do
      user = create_user()

      # Create source file with content
      source_file =
        create_source_file(%{
          name: "test.ex",
          content: "source content",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Use the query to fetch files
      files = Files.source_files_with_content_query() |> Repo.all()

      assert length(files) == 1
      file = hd(files)
      assert file.id == source_file.id
      assert file.content == "source content"
    end

    test "get_source_file_with_content/1 returns source file with content" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "test.ex",
          content: "source content",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Get source file with content
      loaded_file = Files.get_source_file_with_content(source_file.id)

      assert loaded_file.id == source_file.id
      assert loaded_file.content == "source content"
    end

    test "get_source_file_with_content/1 returns nil for non-existent file" do
      assert Files.get_source_file_with_content(Ecto.UUID.generate()) == nil
    end

    test "get_source_file_with_content!/1 raises for non-existent file" do
      assert_raise Ecto.NoResultsError, fn ->
        Files.get_source_file_with_content!(Ecto.UUID.generate())
      end
    end

    test "get_source_files_with_content/1 returns multiple source files" do
      user = create_user()

      file1 =
        create_source_file(%{
          name: "test1.ex",
          content: "content 1",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      file2 =
        create_source_file(%{
          name: "test2.ex",
          content: "content 2",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Get multiple source files
      loaded_files = Files.get_source_files_with_content([file1.id, file2.id])

      assert length(loaded_files) == 2

      loaded_file1 = Enum.find(loaded_files, &(&1.id == file1.id))
      loaded_file2 = Enum.find(loaded_files, &(&1.id == file2.id))

      assert loaded_file1.content == "content 1"
      assert loaded_file2.content == "content 2"
    end
  end

  describe "repository file operations" do
    test "get_repository_file_with_content/2 finds file by path" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/test.ex",
          content: "test content"
        })

      # Get file by path
      found_file = Files.get_repository_file_with_content(repository, "lib/test.ex")

      assert found_file != nil
      assert found_file.path == "lib/test.ex"
      assert found_file.content == "test content"
    end

    test "get_repository_file_with_content/2 returns nil for non-existent path" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      found_file = Files.get_repository_file_with_content(repository, "nonexistent.ex")
      assert found_file == nil
    end

    test "get_repository_files_with_content/2 finds multiple files by paths" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _file1 =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/test1.ex",
          content: "content 1"
        })

      _file2 =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/test2.ex",
          content: "content 2"
        })

      # Get files by paths
      found_files =
        Files.get_repository_files_with_content(repository, ["lib/test1.ex", "lib/test2.ex"])

      assert length(found_files) == 2

      file1 = Enum.find(found_files, &(&1.path == "lib/test1.ex"))
      file2 = Enum.find(found_files, &(&1.path == "lib/test2.ex"))

      assert file1.content == "content 1"
      assert file2.content == "content 2"
    end
  end

  describe "combined repository and source files" do
    test "load_repository_with_all_files/1 loads both repository and source files" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create repository file
      _repo_file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/app.ex",
          content: "repo content"
        })

      # Create source file with source_repository_id to appear in imported_files
      _source_file =
        create_source_file(%{
          name: "config.ex",
          content: "source content",
          target_path: "config/config.ex",
          source_repository_id: repository.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Load repository with all files
      loaded_repo = Files.load_repository_with_all_files(repository)

      assert length(loaded_repo.files) == 1
      assert length(loaded_repo.imported_files) == 1

      repo_file = hd(loaded_repo.files)
      imported_file = hd(loaded_repo.imported_files)

      assert repo_file.content == "repo content"
      assert imported_file.content == "source content"
    end

    test "load_repository_with_all_files/1 with repository ID" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _repo_file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.ex",
          content: "test content"
        })

      # Load by ID
      loaded_repo = Files.load_repository_with_all_files(repository.id)

      assert loaded_repo.id == repository.id
      assert length(loaded_repo.files) == 1
      assert hd(loaded_repo.files).content == "test content"
    end

    test "load_repositories_with_all_files/1 loads multiple repositories with all files" do
      user = create_user()
      repo1 = create_repository(%{user_id: user.id, name: "repo1"})
      repo2 = create_repository(%{user_id: user.id, name: "repo2"})

      _file1 =
        create_repository_file(%{
          repository_id: repo1.id,
          path: "test1.ex",
          content: "content 1"
        })

      _file2 =
        create_repository_file(%{
          repository_id: repo2.id,
          path: "test2.ex",
          content: "content 2"
        })

      # Load multiple repositories with all files
      loaded_repos = Files.load_repositories_with_all_files([repo1, repo2])

      assert length(loaded_repos) == 2

      loaded_repo1 = Enum.find(loaded_repos, &(&1.id == repo1.id))
      loaded_repo2 = Enum.find(loaded_repos, &(&1.id == repo2.id))

      assert length(loaded_repo1.files) == 1
      assert length(loaded_repo2.files) == 1
      assert hd(loaded_repo1.files).content == "content 1"
      assert hd(loaded_repo2.files).content == "content 2"
    end
  end

  describe "source file operations" do
    test "list_source_files_with_content/2 lists files for user and organization" do
      user = create_user()
      organization = Repo.get!(Repobot.Accounts.Organization, user.default_organization_id)

      _file1 =
        create_source_file(%{
          name: "test1.ex",
          content: "content 1",
          user_id: user.id,
          organization_id: organization.id
        })

      _file2 =
        create_source_file(%{
          name: "test2.ex",
          content: "content 2",
          user_id: user.id,
          organization_id: organization.id
        })

      # List source files
      files = Files.list_source_files_with_content(user, organization)

      assert length(files) == 2
      # Files should be ordered by updated_at desc
      assert Enum.all?(files, &(&1.content != nil))
    end

    test "list_source_files_from_repository_with_content/1 lists files from specific repository" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _file1 =
        create_source_file(%{
          name: "test1.ex",
          content: "content 1",
          source_repository_id: repository.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      _file2 =
        create_source_file(%{
          name: "test2.ex",
          content: "content 2",
          source_repository_id: repository.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # List source files from repository
      files = Files.list_source_files_from_repository_with_content(repository)

      assert length(files) == 2
      # Files should be ordered by name asc
      assert Enum.all?(files, &(&1.content != nil))
      assert Enum.all?(files, &(&1.source_repository_id == repository.id))
    end

    test "find_source_files_with_content/2 finds files by repository and paths" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _file1 =
        create_source_file(%{
          name: "test1.ex",
          content: "content 1",
          target_path: "lib/test1.ex",
          source_repository_id: repository.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      _file2 =
        create_source_file(%{
          name: "test2.ex",
          content: "content 2",
          target_path: "lib/test2.ex",
          source_repository_id: repository.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Find specific files
      files =
        Files.find_source_files_with_content(repository.id, ["lib/test1.ex", "lib/test2.ex"])

      assert length(files) == 2
      assert Enum.all?(files, &(&1.content != nil))
      assert Enum.all?(files, &(&1.source_repository_id == repository.id))
    end
  end

  describe "preloading helpers" do
    test "preload_source_files_with_content/2 preloads source files on associations" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _source_file =
        create_source_file(%{
          name: "test.ex",
          content: "source content",
          source_repository_id: repository.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Preload imported_files with content
      loaded_repo = Files.preload_source_files_with_content(repository, :imported_files)

      assert length(loaded_repo.imported_files) == 1
      imported_file = hd(loaded_repo.imported_files)
      assert imported_file.content == "source content"
    end

    test "preload_repository_files_with_content/2 preloads repository files on associations" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      _file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.ex",
          content: "repo content"
        })

      # Preload files with content
      loaded_repo = Files.preload_repository_files_with_content(repository, :files)

      assert length(loaded_repo.files) == 1
      file = hd(loaded_repo.files)
      assert file.content == "repo content"
    end
  end

  describe "content helpers" do
    test "get_content/1 returns content for files with content" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "test.ex",
          content: "source content",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      repository = create_repository(%{user_id: user.id})

      _repo_file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.ex",
          content: "repo content"
        })

      # Load files with content
      loaded_source = Files.get_source_file_with_content(source_file.id)
      loaded_repo = Files.load_repository_with_files(repository)
      loaded_repo_file = hd(loaded_repo.files)

      assert Files.get_content(loaded_source) == "source content"
      assert Files.get_content(loaded_repo_file) == "repo content"
    end

    test "get_content/1 returns empty string for nil content" do
      file_with_nil_content = %{content: nil}
      assert Files.get_content(file_with_nil_content) == ""
    end

    test "get_content/1 returns empty string for invalid input" do
      assert Files.get_content(%{}) == ""
      assert Files.get_content("not a file") == ""
    end

    test "has_content?/1 checks if file has content" do
      file_with_content = %{content: "some content"}
      file_with_empty_content = %{content: ""}
      file_with_nil_content = %{content: nil}

      assert Files.has_content?(file_with_content) == true
      assert Files.has_content?(file_with_empty_content) == false
      assert Files.has_content?(file_with_nil_content) == false
      assert Files.has_content?(%{}) == false
    end
  end

  describe "shared vs exclusive content scenarios" do
    test "handles shared content between repository file and source file" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create source file first
      source_file =
        create_source_file(%{
          name: "shared.ex",
          content: "shared content",
          target_path: "lib/shared.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create repository file that shares content with source file
      _repo_file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/shared.ex",
          content: "shared content"
        })

      # Update source file to have source_repository_id for imported_files association
      source_file = Repo.get!(Repobot.SourceFile, source_file.id)

      {:ok, _updated_source_file} =
        Repobot.SourceFiles.update_source_file(source_file, %{source_repository_id: repository.id})

      # Both should have the same content
      loaded_repo = Files.load_repository_with_all_files(repository)
      loaded_repo_file = hd(loaded_repo.files)
      loaded_source_file = hd(loaded_repo.imported_files)

      assert Files.get_content(loaded_repo_file) == "shared content"
      assert Files.get_content(loaded_source_file) == "shared content"
    end

    test "handles exclusive content for repository files" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create repository file with exclusive content
      _repo_file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "exclusive.ex",
          content: "exclusive repo content"
        })

      loaded_repo = Files.load_repository_with_files(repository)
      file = hd(loaded_repo.files)

      assert Files.get_content(file) == "exclusive repo content"
      assert Files.has_content?(file) == true
    end

    test "handles exclusive content for source files" do
      user = create_user()

      # Create source file with exclusive content
      source_file =
        create_source_file(%{
          name: "exclusive.ex",
          content: "exclusive source content",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      loaded_file = Files.get_source_file_with_content(source_file.id)

      assert Files.get_content(loaded_file) == "exclusive source content"
      assert Files.has_content?(loaded_file) == true
    end
  end
end
