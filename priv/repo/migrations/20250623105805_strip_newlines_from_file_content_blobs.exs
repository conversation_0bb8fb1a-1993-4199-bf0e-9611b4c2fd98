defmodule Repobot.Repo.Migrations.StripNewlinesFromFileContentBlobs do
  use Ecto.Migration

  def up do
    # Strip newlines from blob field in file_contents table
    # This removes the need for the workaround in Base64Content.load/1
    execute """
    UPDATE file_contents
    SET blob = REPLACE(blob, E'\n', '')
    WHERE blob LIKE '%\n%'
    """
  end

  def down do
    # This migration is not reversible since we're removing data (newlines)
    # The original newlines cannot be restored accurately
    raise "This migration cannot be reversed as it removes newline data"
  end
end
