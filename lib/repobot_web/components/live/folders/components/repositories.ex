defmodule RepobotWeb.Live.Folders.Components.Repositories do
  use RepobotWeb, :live_component
  require Logger

  alias Repobot.Workers.RepositoryFilesWorker

  def mount(socket) do
    {:ok, socket}
  end

  def update(assigns, socket) do
    # Initialize loading states and get repositories that need refresh
    all_repositories = assigns.folder.repositories
    {loading_states, needs_refresh} = Repobot.Files.RepoTree.init_loading(all_repositories)

    socket =
      socket
      |> assign(assigns)
      |> assign(:loading_states, loading_states)
      |> assign(:loading_progress, 0)
      |> assign(:loading_message, "")

    # Start loading trees if connected and there are repos to refresh
    if connected?(socket) do
      # Subscribe to repository file changes
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_files")

      unless Enum.empty?(needs_refresh) do
        # Use RepositoryFilesWorker for loading trees
        start_tree_loading(socket, needs_refresh)
      end
    end

    {:ok, socket}
  end

  def render(assigns) do
    ~H"""
    <div>
      <div id="repository-list" class="divide-y divide-slate-200" phx-hook="RepositoryList">
        <%= for repo <- non_template_repositories(@folder.repositories) do %>
          <div class="px-6 py-4" data-repository aria-expanded="false">
            <div class="flex items-center justify-between cursor-pointer">
              <div class="flex items-center gap-2">
                <div data-chevron class="transform transition-transform duration-200">
                  <%= case Repobot.Files.RepoTree.get_loading_state(@loading_states, repo.id) do %>
                    <% :loading -> %>
                      <.icon name="hero-arrow-path" class="w-4 h-4 animate-spin text-indigo-600" />
                    <% _ -> %>
                      <.icon name="hero-chevron-right" class="w-4 h-4 text-slate-400" />
                  <% end %>
                </div>
                <h3 class="text-sm font-medium text-slate-900">
                  {repo.full_name}
                </h3>
              </div>
            </div>
            <div data-source-files class="mt-3 pl-6 hidden">
              <%= if Enum.empty?(repo.source_files || []) do %>
                <p class="text-sm text-slate-500 italic">No source files</p>
              <% else %>
                <div class="flex flex-wrap gap-1.5">
                  <%= for source_file <- (repo.source_files || []) do %>
                    <button
                      phx-click="show_diff"
                      phx-value-source_file_id={source_file.id}
                      phx-value-repository={repo.full_name}
                      phx-target={@myself}
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-slate-600 bg-slate-100 hover:bg-slate-200 hover:text-slate-900 transition-colors duration-150"
                    >
                      {source_file.name}
                    </button>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  # Event handlers
  def handle_event("show_diff", params, socket) do
    # Forward to parent for modal handling
    send(self(), {:repositories_event, "show_diff", params})
    {:noreply, socket}
  end

  # Handle Oban.Notifier messages for repository files loading
  def handle_info(
        {:notification, channel,
         %{"event" => "repository_files_progress", "progress" => progress, "message" => message}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel for this user
    if Atom.to_string(channel)
       |> String.starts_with?("repository_files:#{socket.assigns.current_user.id}") do
      {:noreply,
       socket
       |> assign(:loading_progress, progress)
       |> assign(:loading_message, message)}
    else
      {:noreply, socket}
    end
  end

  def handle_info(
        {:notification, channel, %{"event" => "repository_files_complete", "result" => result}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel for this user
    if Atom.to_string(channel)
       |> String.starts_with?("repository_files:#{socket.assigns.current_user.id}") do
      # Handle repository files loading completion
      case result do
        %{"status" => "ok", "operation" => "load_trees", "repository_ids" => repository_ids} ->
          Logger.info("Repository trees loaded successfully...")

          # Update loading states to mark all repositories as loaded
          updated_loading_states =
            Enum.reduce(repository_ids, socket.assigns.loading_states, fn repo_id, states ->
              Map.put(states, repo_id, :loaded)
            end)

          socket =
            socket
            |> assign(:loading_progress, 100)
            |> assign(:loading_message, "Trees loaded")
            |> assign(:loading_states, updated_loading_states)

          # Notify parent that trees are loaded
          send(self(), {:repositories_event, "trees_loaded", %{}})
          {:noreply, socket}

        %{"status" => "ok", "operation" => "refresh_content", "repository_ids" => _repository_ids} ->
          Logger.info("Repository content refreshed successfully...")
          # Notify parent that content refresh is complete
          send(self(), {:repositories_event, "content_refreshed", %{}})
          {:noreply, socket}

        %{"status" => "error", "reason" => reason} ->
          Logger.error("Repository files operation failed: #{reason}")
          # Notify parent of error
          send(self(), {:repositories_event, "loading_error", %{reason: reason}})
          {:noreply, socket}

        _ ->
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  def handle_info(_msg, socket), do: {:noreply, socket}

  # Private functions
  defp start_tree_loading(socket, repositories) do
    user_id = socket.assigns.current_user.id
    repository_ids = Enum.map(repositories, & &1.id)
    topic = "repository_files:#{user_id}"

    # Subscribe to repository files loading events via Oban.Notifier
    channel = String.to_atom(topic)
    Oban.Notifier.listen([channel])

    case RepositoryFilesWorker.enqueue_tree_loading(user_id, repository_ids, topic) do
      {:ok, _job} ->
        Logger.info("Enqueued tree loading job for #{length(repository_ids)} repositories")

      {:error, reason} ->
        # Fall back to synchronous loading on error
        Logger.warning("Failed to enqueue tree loading job: #{inspect(reason)}")

        Repobot.Files.RepoTree.load_trees(
          repositories,
          self(),
          socket.assigns.current_user
        )
    end
  end

  defp non_template_repositories(repositories) do
    repositories
    |> Enum.reject(& &1.template)
    |> Enum.sort_by(& &1.full_name)
  end
end
